/**
 * Split-Second Spark - 游戏启动器
 * 负责游戏的启动、预览和管理功能
 */

class GameLauncher {
    constructor() {
        this.games = new Map();
        this.currentGame = null;
        this.isInitialized = false;
        
        // 初始化游戏配置
        this.initGameConfigs();
    }

    /**
     * 初始化游戏配置
     */
    initGameConfigs() {
        // 时空织梦者配置
        this.games.set('temporal', {
            id: 'temporal',
            name: 'games.temporal.title',
            subtitle: 'games.temporal.subtitle',
            description: 'games.temporal.description',
            features: ['games.temporal.feature1', 'games.temporal.feature2', 'games.temporal.feature3'],
            path: '时空织梦者/index.html',
            icon: '⏰',
            gradient: 'temporal-gradient',
            animationType: 'particles',
            category: 'puzzle',
            difficulty: 'medium',
            estimatedTime: '30-60分钟',
            requirements: {
                browser: 'Chrome 80+, Firefox 75+, Safari 13+',
                memory: '512MB',
                storage: '50MB'
            },
            controls: {
                'zh-CN': [
                    '鼠标左键：选择/确认',
                    '鼠标右键：取消/返回',
                    '空格键：暂停/继续',
                    'R键：重置当前关卡',
                    'ESC键：返回菜单'
                ],
                'en-US': [
                    'Left Click: Select/Confirm',
                    'Right Click: Cancel/Back',
                    'Space: Pause/Resume',
                    'R Key: Reset Level',
                    'ESC Key: Return to Menu'
                ]
            }
        });

        // 瞬光捕手配置
        this.games.set('spark', {
            id: 'spark',
            name: 'games.spark.title',
            subtitle: 'games.spark.subtitle',
            description: 'games.spark.description',
            features: ['games.spark.feature1', 'games.spark.feature2', 'games.spark.feature3'],
            path: '瞬光捕手/index.html',
            icon: '⚡',
            gradient: 'spark-gradient',
            animationType: 'sparks',
            category: 'action',
            difficulty: 'easy',
            estimatedTime: '10-30分钟',
            requirements: {
                browser: 'Chrome 70+, Firefox 70+, Safari 12+',
                memory: '256MB',
                storage: '20MB'
            },
            controls: {
                'zh-CN': [
                    '鼠标左键：点击捕捉',
                    '空格键：暂停游戏',
                    'R键：重新开始',
                    'ESC键：返回菜单'
                ],
                'en-US': [
                    'Left Click: Click to Catch',
                    'Space: Pause Game',
                    'R Key: Restart',
                    'ESC Key: Return to Menu'
                ]
            }
        });

        // 量子共鸣者配置
        this.games.set('quantum', {
            id: 'quantum',
            name: 'games.quantum.title',
            subtitle: 'games.quantum.subtitle',
            description: 'games.quantum.description',
            features: ['games.quantum.feature1', 'games.quantum.feature2', 'games.quantum.feature3'],
            path: '量子共鸣者/index.html',
            icon: '🌀',
            gradient: 'quantum-gradient',
            animationType: 'quantum',
            category: 'rhythm',
            difficulty: 'hard',
            estimatedTime: '20-45分钟',
            requirements: {
                browser: 'Chrome 85+, Firefox 80+, Safari 14+',
                memory: '1GB',
                storage: '100MB'
            },
            controls: {
                'zh-CN': [
                    '鼠标移动：控制频率',
                    '鼠标左键：激活共鸣',
                    '鼠标右键：停止共鸣',
                    '空格键：暂停/继续',
                    'ESC键：返回菜单'
                ],
                'en-US': [
                    'Mouse Move: Control Frequency',
                    'Left Click: Activate Resonance',
                    'Right Click: Stop Resonance',
                    'Space: Pause/Resume',
                    'ESC Key: Return to Menu'
                ]
            }
        });
    }

    /**
     * 初始化游戏启动器
     */
    async init() {
        try {
            console.log('🎮 初始化游戏启动器...');
            
            // 检查游戏文件是否存在
            await this.validateGameFiles();
            
            this.isInitialized = true;
            console.log('✅ 游戏启动器初始化完成');
            
        } catch (error) {
            console.error('❌ 游戏启动器初始化失败:', error);
            this.isInitialized = true; // 即使失败也标记为已初始化，避免重复初始化
        }
    }

    /**
     * 验证游戏文件是否存在
     */
    async validateGameFiles() {
        const validationPromises = Array.from(this.games.values()).map(async (game) => {
            try {
                const response = await fetch(game.path, { method: 'HEAD' });
                game.available = response.ok;
                if (!response.ok) {
                    console.warn(`⚠️ 游戏文件不存在: ${game.path}`);
                }
            } catch (error) {
                console.warn(`⚠️ 无法验证游戏文件: ${game.path}`, error);
                game.available = false;
            }
        });
        
        await Promise.all(validationPromises);
    }

    /**
     * 启动游戏
     * @param {string} gameId - 游戏ID
     */
    async launchGame(gameId) {
        if (!this.isInitialized) {
            await this.init();
        }

        const game = this.games.get(gameId);
        if (!game) {
            throw new Error(`游戏不存在: ${gameId}`);
        }

        if (game.available === false) {
            throw new Error(`游戏文件不可用: ${gameId}`);
        }

        try {
            console.log(`🚀 启动游戏: ${gameId}`);
            
            // 记录游戏启动
            await this.recordGameLaunch(gameId);
            
            // 设置当前游戏
            this.currentGame = gameId;
            
            // 跳转到游戏页面
            window.location.href = game.path;
            
        } catch (error) {
            console.error(`❌ 启动游戏失败 [${gameId}]:`, error);
            throw error;
        }
    }

    /**
     * 记录游戏启动
     * @param {string} gameId - 游戏ID
     */
    async recordGameLaunch(gameId) {
        try {
            if (typeof storageService !== 'undefined') {
                // 记录启动时间
                const launchRecord = {
                    gameId,
                    timestamp: Date.now(),
                    date: new Date().toISOString()
                };
                
                await storageService.put(`launch_${gameId}_${Date.now()}`, launchRecord);
                
                // 更新游戏统计
                const stats = await this.getGameStats(gameId);
                stats.launchCount = (stats.launchCount || 0) + 1;
                stats.lastLaunch = Date.now();
                
                await storageService.put(`stats_${gameId}`, stats);
            }
        } catch (error) {
            console.warn('⚠️ 记录游戏启动失败:', error);
        }
    }

    /**
     * 获取游戏统计信息
     * @param {string} gameId - 游戏ID
     */
    async getGameStats(gameId) {
        try {
            if (typeof storageService !== 'undefined') {
                const stats = await storageService.get(`stats_${gameId}`);
                return stats || {
                    gameId,
                    launchCount: 0,
                    lastLaunch: null,
                    totalPlayTime: 0
                };
            }
        } catch (error) {
            console.warn('⚠️ 获取游戏统计失败:', error);
        }
        
        return {
            gameId,
            launchCount: 0,
            lastLaunch: null,
            totalPlayTime: 0
        };
    }

    /**
     * 获取游戏信息
     * @param {string} gameId - 游戏ID
     */
    getGameInfo(gameId) {
        return this.games.get(gameId);
    }

    /**
     * 获取所有游戏列表
     */
    getAllGames() {
        return Array.from(this.games.values());
    }

    /**
     * 获取可用游戏列表
     */
    getAvailableGames() {
        return Array.from(this.games.values()).filter(game => game.available !== false);
    }

    /**
     * 生成游戏预览内容
     * @param {string} gameId - 游戏ID
     * @param {string} language - 语言代码
     */
    generatePreviewContent(gameId, language = 'zh-CN') {
        const game = this.games.get(gameId);
        if (!game) {
            return '<p>游戏不存在</p>';
        }

        const t = (key) => {
            if (typeof i18nService !== 'undefined') {
                return i18nService.t(key);
            }
            return key;
        };

        const controls = game.controls[language] || game.controls['zh-CN'] || [];
        const controlsHtml = controls.map(control => `<li>${control}</li>`).join('');

        return `
            <div class="preview-content">
                <div class="preview-section">
                    <h4>${t('preview.features')}</h4>
                    <ul class="feature-list">
                        ${game.features.map(feature => `<li>${t(feature)}</li>`).join('')}
                    </ul>
                </div>
                
                <div class="preview-section">
                    <h4>${t('preview.controls')}</h4>
                    <ul class="controls-list">
                        ${controlsHtml}
                    </ul>
                </div>
                
                <div class="preview-section">
                    <h4>${t('preview.requirements')}</h4>
                    <div class="requirements">
                        <p><strong>浏览器:</strong> ${game.requirements.browser}</p>
                        <p><strong>内存:</strong> ${game.requirements.memory}</p>
                        <p><strong>存储:</strong> ${game.requirements.storage}</p>
                        <p><strong>预计游戏时间:</strong> ${game.estimatedTime}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 检查游戏兼容性
     * @param {string} gameId - 游戏ID
     */
    checkGameCompatibility(gameId) {
        const game = this.games.get(gameId);
        if (!game) {
            return { compatible: false, reason: '游戏不存在' };
        }

        // 检查浏览器兼容性
        const userAgent = navigator.userAgent;
        const isChrome = /Chrome/.test(userAgent);
        const isFirefox = /Firefox/.test(userAgent);
        const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);

        if (!isChrome && !isFirefox && !isSafari) {
            return { 
                compatible: false, 
                reason: '不支持的浏览器，建议使用 Chrome、Firefox 或 Safari' 
            };
        }

        // 检查特殊要求
        if (gameId === 'quantum') {
            if (!window.AudioContext && !window.webkitAudioContext) {
                return { 
                    compatible: false, 
                    reason: '浏览器不支持 Web Audio API，无法运行量子共鸣者' 
                };
            }
        }

        return { compatible: true };
    }

    /**
     * 获取推荐游戏
     * @param {string} excludeGameId - 要排除的游戏ID
     */
    async getRecommendedGames(excludeGameId = null) {
        const availableGames = this.getAvailableGames().filter(game => 
            game.id !== excludeGameId
        );

        // 简单的推荐算法：基于启动次数和最后启动时间
        const gamesWithStats = await Promise.all(
            availableGames.map(async (game) => {
                const stats = await this.getGameStats(game.id);
                return { ...game, stats };
            })
        );

        // 按推荐度排序
        gamesWithStats.sort((a, b) => {
            const scoreA = (a.stats.launchCount || 0) * 0.7 + 
                          (a.stats.lastLaunch ? (Date.now() - a.stats.lastLaunch) / (1000 * 60 * 60 * 24) : 30) * 0.3;
            const scoreB = (b.stats.launchCount || 0) * 0.7 + 
                          (b.stats.lastLaunch ? (Date.now() - b.stats.lastLaunch) / (1000 * 60 * 60 * 24) : 30) * 0.3;
            return scoreB - scoreA;
        });

        return gamesWithStats.slice(0, 2);
    }
}

// 创建全局游戏启动器实例
const gameLauncher = new GameLauncher();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GameLauncher, gameLauncher };
} else {
    window.gameLauncher = gameLauncher;
}
