/**
 * Split-Second Spark - 主样式文件
 * 包含全局样式、基础组件和通用动画效果
 */

/* ===== 全局变量 ===== */
:root {
    /* 主色调 */
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --accent-color: #45b7d1;
    --warning-color: #f9ca24;
    --success-color: #6c5ce7;
    
    /* 背景色 */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: rgba(26, 26, 46, 0.8);
    --bg-modal: rgba(15, 15, 35, 0.95);
    
    /* 文字颜色 */
    --text-primary: #ffffff;
    --text-secondary: #b8b8d1;
    --text-muted: #8b8ba7;
    --text-accent: #ff6b6b;
    
    /* 边框和阴影 */
    --border-color: rgba(255, 255, 255, 0.1);
    --border-hover: rgba(255, 107, 107, 0.3);
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.2);
    --shadow-heavy: 0 8px 40px rgba(0, 0, 0, 0.3);
    --glow-primary: 0 0 20px rgba(255, 107, 107, 0.3);
    --glow-secondary: 0 0 20px rgba(78, 205, 196, 0.3);
    
    /* 渐变 */
    --gradient-primary: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
    --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    
    /* 动画时长 */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* 字体大小 */
    --font-xs: 0.75rem;
    --font-sm: 0.875rem;
    --font-md: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    --font-xxl: 1.5rem;
    --font-title: 2rem;
    --font-hero: 3rem;
}

/* ===== 基础重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--gradient-background);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* ===== 通用类 ===== */
.screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal) ease-in-out;
    z-index: 1;
}

.screen.active {
    opacity: 1;
    visibility: visible;
    z-index: 10;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* ===== 加载屏幕 ===== */
#loading-screen {
    background: var(--gradient-background);
    z-index: 1000;
}

.loading-container {
    text-align: center;
    max-width: 400px;
    padding: var(--spacing-xl);
}

.spark-logo {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto var(--spacing-xl);
}

.spark-core {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: var(--glow-primary);
    animation: pulse 2s ease-in-out infinite;
}

.spark-rays {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    transform: translate(-50%, -50%);
    animation: rotate 4s linear infinite;
}

.ray {
    position: absolute;
    width: 3px;
    height: 30px;
    background: linear-gradient(to top, transparent, var(--primary-color));
    border-radius: 2px;
    transform-origin: center bottom;
}

.ray:nth-child(1) { transform: rotate(0deg) translateY(-45px); }
.ray:nth-child(2) { transform: rotate(90deg) translateY(-45px); }
.ray:nth-child(3) { transform: rotate(180deg) translateY(-45px); }
.ray:nth-child(4) { transform: rotate(270deg) translateY(-45px); }

.main-title {
    font-size: var(--font-hero);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
    animation: fadeInUp 1s ease-out 0.5s both;
}

.main-subtitle {
    font-size: var(--font-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    animation: fadeInUp 1s ease-out 0.7s both;
}

.loading-progress {
    animation: fadeInUp 1s ease-out 0.9s both;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    width: 0%;
    transition: width var(--transition-normal) ease-out;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
    animation: shimmer 1.5s ease-in-out infinite;
}

.loading-text {
    font-size: var(--font-sm);
    color: var(--text-muted);
    text-align: center;
}

/* ===== 动画定义 ===== */
@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmer {
    0% { transform: translateX(-20px); }
    100% { transform: translateX(100px); }
}

@keyframes glow {
    0%, 100% { box-shadow: var(--glow-primary); }
    50% { box-shadow: 0 0 30px rgba(255, 107, 107, 0.5); }
}

/* ===== 按钮样式 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: 8px;
    font-size: var(--font-md);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-normal) ease;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--border-hover);
    background: var(--bg-secondary);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    :root {
        --font-hero: 2rem;
        --font-title: 1.5rem;
        --spacing-xl: 1.5rem;
        --spacing-xxl: 2rem;
    }
    
    .main-title {
        font-size: var(--font-title);
    }
    
    .spark-logo {
        width: 80px;
        height: 80px;
    }
    
    .spark-core {
        width: 30px;
        height: 30px;
    }
    
    .ray {
        height: 20px;
        transform-origin: center bottom;
    }
    
    .ray:nth-child(1) { transform: rotate(0deg) translateY(-30px); }
    .ray:nth-child(2) { transform: rotate(90deg) translateY(-30px); }
    .ray:nth-child(3) { transform: rotate(180deg) translateY(-30px); }
    .ray:nth-child(4) { transform: rotate(270deg) translateY(-30px); }
}

@media (max-width: 480px) {
    .loading-container {
        padding: var(--spacing-lg);
    }
    
    .main-title {
        font-size: var(--font-xxl);
    }
    
    .main-subtitle {
        font-size: var(--font-md);
    }
}
